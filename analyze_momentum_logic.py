#!/usr/bin/env python3
"""
Script to analyze momentum logic in allocation history CSV
"""
import pandas as pd
import ast
from datetime import datetime

def analyze_momentum_logic(csv_file):
    """Analyze momentum logic in allocation history"""

    # Read CSV
    df = pd.read_csv(csv_file)

    # Convert scores from string to dict
    df['scores_dict'] = df['scores'].apply(lambda x: ast.literal_eval(x) if pd.notna(x) else {})

    # Track issues
    issues = []
    momentum_violations = []

    print("=== COMPREHENSIVE MOMENTUM LOGIC ANALYSIS ===\n")

    def extract_asset(allocation_str):
        """Extract asset name from allocation string"""
        if pd.isna(allocation_str) or allocation_str == '[]':
            return None
        if isinstance(allocation_str, str) and allocation_str.startswith('['):
            try:
                assets = ast.literal_eval(allocation_str)
                return assets[0] if assets else None
            except:
                return None
        return allocation_str

    # Check EVERY day for momentum logic violations, not just switches
    for i in range(1, len(df)):
        current_row = df.iloc[i]
        prev_row = df.iloc[i-1]

        current_date = current_row['date']
        current_allocation = extract_asset(current_row['current_holdings'])
        prev_allocation = extract_asset(prev_row['current_holdings'])
        current_scores = current_row['scores_dict']
        prev_scores = prev_row['scores_dict']

        # Skip if no allocation (mtpi_signal = -1)
        if current_row['mtpi_signal'] == -1:
            continue

        # Skip if no current allocation
        if not current_allocation:
            continue

        # Find highest scoring assets for current day
        if not current_scores:
            continue

        max_score = max(current_scores.values())
        top_assets = [asset for asset, score in current_scores.items() if score == max_score]

        # Check if allocation is correct based on momentum logic
        allocation_correct = False
        expected_asset = None
        violation_reason = ""

        if len(top_assets) == 1:
            # Clear winner case
            expected_asset = top_assets[0]
            allocation_correct = (current_allocation == expected_asset)
            if not allocation_correct:
                violation_reason = f"Should select {expected_asset} (highest score {max_score}), but selected {current_allocation}"
        else:
            # Tie situation - apply momentum logic
            momentum_analysis = {}
            for asset in top_assets:
                prev_score = prev_scores.get(asset, 0)
                current_score = current_scores[asset]
                delta = current_score - prev_score
                momentum_analysis[asset] = {
                    'prev_score': prev_score,
                    'current_score': current_score,
                    'delta': delta
                }

            # Find asset(s) with best momentum
            best_momentum = max(momentum_analysis.values(), key=lambda x: x['delta'])['delta']
            best_momentum_assets = [asset for asset, data in momentum_analysis.items()
                                  if data['delta'] == best_momentum]

            if len(best_momentum_assets) == 1:
                # Clear momentum winner
                expected_asset = best_momentum_assets[0]
                allocation_correct = (current_allocation == expected_asset)
                if not allocation_correct:
                    current_momentum = momentum_analysis[current_allocation]['delta'] if current_allocation in momentum_analysis else "N/A"
                    violation_reason = f"TIE at score {max_score}: Should select {expected_asset} (momentum Δ={best_momentum:+.1f}), but selected {current_allocation} (momentum Δ={current_momentum:+.1f})"
            else:
                # Still tied after momentum - use order preference
                asset_order = ['ETH/USDT', 'BTC/USDT', 'SOL/USDT', 'SUI/USDT', 'XRP/USDT']
                ordered_tied = [asset for asset in asset_order if asset in best_momentum_assets]
                if ordered_tied:
                    expected_asset = ordered_tied[0]
                    allocation_correct = (current_allocation == expected_asset)
                    if not allocation_correct:
                        violation_reason = f"TIE in momentum (Δ={best_momentum:+.1f}): Should select {expected_asset} (first in order), but selected {current_allocation}"
                else:
                    # Fallback - just check if selected asset is among the tied assets
                    allocation_correct = (current_allocation in best_momentum_assets)
                    if not allocation_correct:
                        violation_reason = f"Selected {current_allocation} not among best momentum assets: {best_momentum_assets}"

        # Record violations
        if not allocation_correct:
            is_switch = (current_allocation != prev_allocation)
            violation_type = "SWITCH VIOLATION" if is_switch else "HOLD VIOLATION"

            violation = {
                'date': current_date,
                'type': violation_type,
                'current_allocation': current_allocation,
                'prev_allocation': prev_allocation,
                'expected_asset': expected_asset,
                'current_scores': current_scores,
                'prev_scores': prev_scores,
                'top_assets': top_assets,
                'reason': violation_reason,
                'is_switch': is_switch
            }

            momentum_violations.append(violation)

            # Print detailed violation info
            print(f"\n[X] {violation_type} on {current_date}")
            print(f"   Allocation: {prev_allocation} -> {current_allocation}")
            print(f"   Current scores: {current_scores}")
            print(f"   Previous scores: {prev_scores}")
            print(f"   [!] VIOLATION: {violation_reason}")

            if len(top_assets) > 1:
                print(f"   [M] Momentum analysis for tied assets:")
                for asset in top_assets:
                    prev_score = prev_scores.get(asset, 0)
                    current_score = current_scores[asset]
                    delta = current_score - prev_score
                    momentum_indicator = "UP" if delta > 0 else "FLAT" if delta == 0 else "DOWN"
                    print(f"     {asset}: {prev_score} -> {current_score} (Delta={delta:+.1f}) {momentum_indicator}")

    return momentum_violations

    print(f"\n=== SUMMARY ===")
    print(f"Total momentum violations found: {len(momentum_violations)}")

    # Categorize violations
    switch_violations = [v for v in momentum_violations if v['is_switch']]
    hold_violations = [v for v in momentum_violations if not v['is_switch']]

    print(f"  - Switch violations (wrong asset selected during switch): {len(switch_violations)}")
    print(f"  - Hold violations (should have switched but didn't): {len(hold_violations)}")

    if momentum_violations:
        print("\n[!] MOMENTUM VIOLATIONS DETECTED:")

        print(f"\n[S] SWITCH VIOLATIONS ({len(switch_violations)}):")
        for v in switch_violations:
            print(f"  {v['date']}: {v['reason']}")

        print(f"\n[H] HOLD VIOLATIONS ({len(hold_violations)}):")
        for v in hold_violations:
            print(f"  {v['date']}: {v['reason']}")

        # Analyze patterns
        print(f"\n[P] PATTERN ANALYSIS:")

        # Check if it's using incumbent approach instead
        incumbent_like = 0
        for v in hold_violations:
            if v['current_allocation'] == v['prev_allocation']:
                incumbent_like += 1

        if incumbent_like > len(hold_violations) * 0.7:
            print(f"  [I] LIKELY USING INCUMBENT APPROACH: {incumbent_like}/{len(hold_violations)} hold violations suggest")
            print(f"     the system keeps current asset until definitively beaten, rather than switching on momentum")

        # Most problematic periods
        violation_dates = [v['date'] for v in momentum_violations]
        if violation_dates:
            print(f"  [D] First violation: {min(violation_dates)}")
            print(f"  [D] Last violation: {max(violation_dates)}")

    else:
        print("\n[OK] All momentum logic appears correct!")

    return momentum_violations

if __name__ == "__main__":
    csv_file = "allocation_history_1d_1d_with_mtpi_no_rebal_2023-10-19.csv"
    issues = analyze_momentum_logic(csv_file)
